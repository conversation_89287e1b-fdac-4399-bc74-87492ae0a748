<!DOCTYPE html>
<html>
<head>
    <title>Upload Test</title>
    <style>
        .border-dashed {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px;
            text-align: center;
            cursor: pointer;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Upload Test</h1>
    
    <div class="border-dashed">
        <input type="file" class="hidden" accept=".xlsx,.xls,.csv" />
        <p>Click to upload File A</p>
        <p>Excel (.xlsx, .xls) or CSV files up to 50MB</p>
    </div>
    
    <div class="border-dashed">
        <input type="file" class="hidden" accept=".xlsx,.xls,.csv" />
        <p>Click to upload File B</p>
        <p>Excel (.xlsx, .xls) or CSV files up to 50MB</p>
    </div>

    <script>
        // Make upload areas clickable
        document.addEventListener('DOMContentLoaded', function() {
          // Function to make upload areas clickable
          function makeUploadAreasClickable() {
            // Find all upload areas (divs with border-dashed that contain hidden file inputs)
            const uploadAreas = document.querySelectorAll('.border-dashed');

            uploadAreas.forEach(uploadArea => {
              // Find the hidden file input within this upload area
              const fileInput = uploadArea.querySelector('input[type="file"]');

              if (fileInput && fileInput.classList.contains('hidden')) {
                // Make the upload area clickable
                uploadArea.style.cursor = 'pointer';

                // Add click event listener
                uploadArea.addEventListener('click', function(e) {
                  // Prevent event bubbling
                  e.preventDefault();
                  e.stopPropagation();

                  // Trigger the hidden file input
                  fileInput.click();
                });

                // Add drag and drop functionality
                uploadArea.addEventListener('dragover', function(e) {
                  e.preventDefault();
                  e.stopPropagation();
                  uploadArea.style.borderColor = '#3b82f6';
                  uploadArea.style.backgroundColor = '#eff6ff';
                });

                uploadArea.addEventListener('dragleave', function(e) {
                  e.preventDefault();
                  e.stopPropagation();
                  uploadArea.style.borderColor = '#ccc';
                  uploadArea.style.backgroundColor = 'transparent';
                });

                uploadArea.addEventListener('drop', function(e) {
                  e.preventDefault();
                  e.stopPropagation();
                  uploadArea.style.borderColor = '#ccc';
                  uploadArea.style.backgroundColor = 'transparent';

                  // Handle dropped files
                  const files = e.dataTransfer.files;
                  if (files.length > 0) {
                    // Set the files to the file input
                    fileInput.files = files;
                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    fileInput.dispatchEvent(event);
                    
                    // Show selected file
                    uploadArea.innerHTML = `<p>Selected: ${files[0].name}</p>`;
                  }
                });
                
                // Handle file selection
                fileInput.addEventListener('change', function(e) {
                  if (e.target.files.length > 0) {
                    uploadArea.innerHTML = `<p>Selected: ${e.target.files[0].name}</p>`;
                  }
                });
              }
            });
          }

          // Initial setup
          makeUploadAreasClickable();
        });
    </script>
</body>
</html>
